const express = require('express');
const router = express.Router();
const Task = require('../models/Task');
const User = require('../models/User');
const Notification = require('../models/Notification');
const { authenticateToken } = require('../middleware/auth');
const { validateTaskCreation, validateTaskUpdate, validateId, validateTaskFilters } = require('../middleware/validation');

// Get all tasks (with optional filters)
router.get('/', authenticateToken, validateTaskFilters, async (req, res) => {
  try {
    const { status, priority, user_id, limit = 50, offset = 0 } = req.query;
    
    // Build filters
    const filters = {};
    if (status) filters.status = status;
    if (priority) filters.priority = priority;
    
    // Non-admin users can only see their own tasks unless specified otherwise
    if (req.user.role !== 'admin') {
      filters.user_id = req.user.id;
    } else if (user_id) {
      filters.user_id = parseInt(user_id);
    }
    
    const tasks = await Task.findAll(filters);
    
    // Apply pagination
    const startIndex = parseInt(offset);
    const endIndex = startIndex + parseInt(limit);
    const paginatedTasks = tasks.slice(startIndex, endIndex);
    
    res.json({
      success: true,
      message: 'Tasks retrieved successfully',
      data: {
        tasks: paginatedTasks,
        pagination: {
          total: tasks.length,
          limit: parseInt(limit),
          offset: parseInt(offset),
          hasMore: endIndex < tasks.length
        }
      }
    });
  } catch (error) {
    console.error('Get tasks error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve tasks',
      error: error.message
    });
  }
});

// Get users for task assignment (must be before /:id route)
router.get('/users', authenticateToken, async (req, res) => {
  try {
    // Only admins can see all users for assignment
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Admin privileges required.'
      });
    }

    const users = await User.findAll();

    // Return only necessary user information
    const userList = users.map(user => ({
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role
    }));

    res.json({
      success: true,
      message: 'Users retrieved successfully',
      data: {
        users: userList
      }
    });
  } catch (error) {
    console.error('Get users for assignment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve users',
      error: error.message
    });
  }
});

// Get task by ID
router.get('/:id', authenticateToken, validateId, async (req, res) => {
  try {
    const task = await Task.findById(req.params.id);
    
    if (!task) {
      return res.status(404).json({
        success: false,
        message: 'Task not found'
      });
    }
    
    // Check ownership or admin access
    if (req.user.role !== 'admin' && task.user_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You can only view your own tasks.'
      });
    }
    
    res.json({
      success: true,
      message: 'Task retrieved successfully',
      data: {
        task
      }
    });
  } catch (error) {
    console.error('Get task error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve task',
      error: error.message
    });
  }
});

// Create new task
router.post('/', authenticateToken, validateTaskCreation, async (req, res) => {
  try {
    const { title, description, status, priority, user_id, start_date, end_date } = req.body;

    // Determine the user_id for the task
    let taskUserId = req.user.id; // Default to current user
    let assignedBy = null;

    // Admin can create tasks for other users
    if (req.user.role === 'admin' && user_id) {
      taskUserId = user_id;
      assignedBy = req.user.id; // Track who assigned the task
    }

    const task = await Task.create({
      title,
      description,
      status,
      priority,
      user_id: taskUserId,
      assigned_by: assignedBy,
      start_date,
      end_date
    });

    // Create notifications for task creation
    try {
      await Notification.createTaskNotification(task, 'created', req.user.id);
    } catch (notificationError) {
      console.error('Failed to create task notification:', notificationError);
      // Don't fail the task creation if notification fails
    }

    res.status(201).json({
      success: true,
      message: 'Task created successfully',
      data: {
        task
      }
    });
  } catch (error) {
    console.error('Create task error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create task',
      error: error.message
    });
  }
});

// Update task
router.put('/:id', authenticateToken, validateId, validateTaskUpdate, async (req, res) => {
  try {
    const task = await Task.findById(req.params.id);
    
    if (!task) {
      return res.status(404).json({
        success: false,
        message: 'Task not found'
      });
    }
    
    // Check ownership or admin access
    if (req.user.role !== 'admin' && task.user_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You can only update your own tasks.'
      });
    }
    
    const updatedTask = await Task.update(req.params.id, req.body);

    // Create notifications for task update
    try {
      await Notification.createTaskNotification(updatedTask, 'updated', req.user.id);
    } catch (notificationError) {
      console.error('Failed to create task update notification:', notificationError);
      // Don't fail the task update if notification fails
    }

    res.json({
      success: true,
      message: 'Task updated successfully',
      data: {
        task: updatedTask
      }
    });
  } catch (error) {
    console.error('Update task error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update task',
      error: error.message
    });
  }
});

// Delete task
router.delete('/:id', authenticateToken, validateId, async (req, res) => {
  try {
    const task = await Task.findById(req.params.id);
    
    if (!task) {
      return res.status(404).json({
        success: false,
        message: 'Task not found'
      });
    }
    
    // Check ownership or admin access
    if (req.user.role !== 'admin' && task.user_id !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You can only delete your own tasks.'
      });
    }
    
    // Create notifications for task deletion before deleting
    try {
      await Notification.createTaskNotification(task, 'deleted', req.user.id);
    } catch (notificationError) {
      console.error('Failed to create task deletion notification:', notificationError);
      // Don't fail the task deletion if notification fails
    }

    const deleted = await Task.delete(req.params.id);

    if (!deleted) {
      return res.status(500).json({
        success: false,
        message: 'Failed to delete task'
      });
    }

    res.json({
      success: true,
      message: 'Task deleted successfully'
    });
  } catch (error) {
    console.error('Delete task error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete task',
      error: error.message
    });
  }
});

// Get task statistics
router.get('/stats/overview', authenticateToken, async (req, res) => {
  try {
    // Non-admin users get their own stats only
    const userId = req.user.role === 'admin' ? null : req.user.id;
    const stats = await Task.getStats(userId);
    
    res.json({
      success: true,
      message: 'Task statistics retrieved successfully',
      data: {
        stats
      }
    });
  } catch (error) {
    console.error('Get task stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve task statistics',
      error: error.message
    });
  }
});

// Get recent tasks
router.get('/recent/list', authenticateToken, async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 5;
    
    // Non-admin users get their own recent tasks only
    const userId = req.user.role === 'admin' ? null : req.user.id;
    const recentTasks = await Task.getRecent(limit, userId);
    
    res.json({
      success: true,
      message: 'Recent tasks retrieved successfully',
      data: {
        tasks: recentTasks
      }
    });
  } catch (error) {
    console.error('Get recent tasks error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve recent tasks',
      error: error.message
    });
  }
});

module.exports = router;
