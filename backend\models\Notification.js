const { executeQuery } = require('./database');

class Notification {
  static async create(notificationData) {
    const { user_id, message, type = 'info', task_id = null, related_user_id = null } = notificationData;
    
    const query = `
      INSERT INTO notifications (user_id, message, type, task_id, related_user_id, is_read, created_at)
      VALUES (?, ?, ?, ?, ?, FALSE, NOW())
    `;
    
    const result = await executeQuery(query, [user_id, message, type, task_id, related_user_id]);
    
    if (result.insertId) {
      return await this.findById(result.insertId);
    }
    
    throw new Error('Failed to create notification');
  }

  static async findById(id) {
    const query = `
      SELECT n.*, u.name as user_name, u.email as user_email,
             ru.name as related_user_name, ru.email as related_user_email,
             t.title as task_title
      FROM notifications n
      LEFT JOIN users u ON n.user_id = u.id
      LEFT JOIN users ru ON n.related_user_id = ru.id
      LEFT JOIN tasks t ON n.task_id = t.id
      WHERE n.id = ?
    `;
    
    const results = await executeQuery(query, [id]);
    return results.length > 0 ? results[0] : null;
  }

  static async findByUserId(userId, options = {}) {
    const { limit = 50, offset = 0, unreadOnly = false } = options;
    
    let query = `
      SELECT n.*, u.name as user_name, u.email as user_email,
             ru.name as related_user_name, ru.email as related_user_email,
             t.title as task_title
      FROM notifications n
      LEFT JOIN users u ON n.user_id = u.id
      LEFT JOIN users ru ON n.related_user_id = ru.id
      LEFT JOIN tasks t ON n.task_id = t.id
      WHERE n.user_id = ?
    `;
    
    const params = [userId];
    
    if (unreadOnly) {
      query += ' AND n.is_read = FALSE';
    }
    
    query += ' ORDER BY n.created_at DESC LIMIT ? OFFSET ?';
    params.push(limit, offset);
    
    return await executeQuery(query, params);
  }

  static async markAsRead(id, userId) {
    const query = `
      UPDATE notifications 
      SET is_read = TRUE, updated_at = NOW()
      WHERE id = ? AND user_id = ?
    `;
    
    const result = await executeQuery(query, [id, userId]);
    return result.affectedRows > 0;
  }

  static async markAllAsRead(userId) {
    const query = `
      UPDATE notifications 
      SET is_read = TRUE, updated_at = NOW()
      WHERE user_id = ? AND is_read = FALSE
    `;
    
    const result = await executeQuery(query, [userId]);
    return result.affectedRows;
  }

  static async getUnreadCount(userId) {
    const query = `
      SELECT COUNT(*) as count
      FROM notifications
      WHERE user_id = ? AND is_read = FALSE
    `;
    
    const results = await executeQuery(query, [userId]);
    return results[0].count;
  }

  static async delete(id, userId) {
    const query = `
      DELETE FROM notifications
      WHERE id = ? AND user_id = ?
    `;
    
    const result = await executeQuery(query, [id, userId]);
    return result.affectedRows > 0;
  }

  static async deleteOld(daysOld = 30) {
    const query = `
      DELETE FROM notifications
      WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
    `;
    
    const result = await executeQuery(query, [daysOld]);
    return result.affectedRows;
  }

  // Helper method to create task-related notifications
  static async createTaskNotification(taskData, action, actorUserId) {
    const { id: taskId, title, user_id: assignedUserId } = taskData;
    const notifications = [];

    // Get actor user info
    const actorQuery = 'SELECT name FROM users WHERE id = ?';
    const actorResult = await executeQuery(actorQuery, [actorUserId]);
    const actorName = actorResult[0]?.name || 'Someone';

    // Get all admin users
    const adminQuery = 'SELECT id FROM users WHERE role = "admin"';
    const adminUsers = await executeQuery(adminQuery);

    let message = '';
    let type = 'info';

    switch (action) {
      case 'created':
        message = `${actorName} created a new task: "${title}"`;
        type = 'success';
        break;
      case 'updated':
        message = `${actorName} updated task: "${title}"`;
        type = 'info';
        break;
      case 'deleted':
        message = `${actorName} deleted task: "${title}"`;
        type = 'warning';
        break;
      case 'assigned':
        message = `${actorName} assigned you to task: "${title}"`;
        type = 'info';
        break;
      default:
        message = `Task "${title}" was ${action} by ${actorName}`;
    }

    // Notify assigned user (if different from actor)
    if (assignedUserId && assignedUserId !== actorUserId) {
      notifications.push({
        user_id: assignedUserId,
        message: action === 'assigned' ? `${actorName} assigned you to task: "${title}"` : message,
        type,
        task_id: taskId,
        related_user_id: actorUserId
      });
    }

    // Notify all admins (except the actor if they're admin)
    for (const admin of adminUsers) {
      if (admin.id !== actorUserId && admin.id !== assignedUserId) {
        notifications.push({
          user_id: admin.id,
          message,
          type,
          task_id: taskId,
          related_user_id: actorUserId
        });
      }
    }

    // Create all notifications
    const createdNotifications = [];
    for (const notificationData of notifications) {
      try {
        const notification = await this.create(notificationData);
        createdNotifications.push(notification);
      } catch (error) {
        console.error('Failed to create notification:', error);
      }
    }

    return createdNotifications;
  }

  // Helper method to create user-related notifications
  static async createUserNotification(userData, action, actorUserId) {
    const { id: userId, name, email } = userData;
    const notifications = [];

    // Get actor user info
    const actorQuery = 'SELECT name FROM users WHERE id = ?';
    const actorResult = await executeQuery(actorQuery, [actorUserId]);
    const actorName = actorResult[0]?.name || 'Someone';

    // Get all admin users
    const adminQuery = 'SELECT id FROM users WHERE role = "admin"';
    const adminUsers = await executeQuery(adminQuery);

    let message = '';
    let type = 'info';

    switch (action) {
      case 'created':
        message = `${actorName} created a new user account for ${name}`;
        type = 'success';
        break;
      case 'updated':
        message = `${actorName} updated user account: ${name}`;
        type = 'info';
        break;
      case 'deleted':
        message = `${actorName} deleted user account: ${name}`;
        type = 'warning';
        break;
      default:
        message = `User ${name} was ${action} by ${actorName}`;
    }

    // Notify all admins (except the actor)
    for (const admin of adminUsers) {
      if (admin.id !== actorUserId) {
        notifications.push({
          user_id: admin.id,
          message,
          type,
          task_id: null,
          related_user_id: actorUserId
        });
      }
    }

    // Create all notifications
    const createdNotifications = [];
    for (const notificationData of notifications) {
      try {
        const notification = await this.create(notificationData);
        createdNotifications.push(notification);
      } catch (error) {
        console.error('Failed to create notification:', error);
      }
    }

    return createdNotifications;
  }
}

module.exports = Notification;
