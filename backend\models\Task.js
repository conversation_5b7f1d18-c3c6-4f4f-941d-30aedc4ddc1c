const { executeQuery } = require('./database');

class Task {
  constructor(data) {
    this.id = data.id;
    this.title = data.title;
    this.description = data.description;
    this.status = data.status;
    this.priority = data.priority;
    this.user_id = data.user_id;
    this.assigned_by = data.assigned_by;
    this.start_date = data.start_date;
    this.end_date = data.end_date;
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;

    // Include user info if available
    if (data.user_name) {
      this.user = {
        id: data.user_id,
        name: data.user_name,
        email: data.user_email
      };
    }

    // Include assigned_by user info if available
    if (data.assigned_by_name) {
      this.assigned_by_user = {
        id: data.assigned_by,
        name: data.assigned_by_name,
        email: data.assigned_by_email
      };
    }

    // Ensure date fields are properly formatted
    if (this.start_date) {
      this.start_date = this.start_date.toISOString().split('T')[0]; // Format as YYYY-MM-DD
    }
    if (this.end_date) {
      this.end_date = this.end_date.toISOString().split('T')[0]; // Format as YYYY-MM-DD
    }
  }

  // Create a new task
  static async create(taskData) {
    const {
      title,
      description,
      status = 'todo',
      priority = 'medium',
      user_id,
      assigned_by = null,
      start_date = null,
      end_date = null
    } = taskData;

    const query = `
      INSERT INTO tasks (title, description, status, priority, user_id, assigned_by, start_date, end_date)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const result = await executeQuery(query, [
      title,
      description,
      status,
      priority,
      user_id,
      assigned_by,
      start_date,
      end_date
    ]);

    return await Task.findById(result.insertId);
  }

  // Find task by ID
  static async findById(id) {
    const query = `
      SELECT t.*,
             u.name as user_name,
             u.email as user_email,
             ab.name as assigned_by_name,
             ab.email as assigned_by_email
      FROM tasks t
      LEFT JOIN users u ON t.user_id = u.id
      LEFT JOIN users ab ON t.assigned_by = ab.id
      WHERE t.id = ?
    `;

    const results = await executeQuery(query, [id]);

    if (results.length === 0) {
      return null;
    }

    const taskData = results[0];

    // Process the raw data to include all fields
    const task = {
      id: taskData.id,
      title: taskData.title,
      description: taskData.description,
      status: taskData.status,
      priority: taskData.priority,
      user_id: taskData.user_id,
      assigned_by: taskData.assigned_by,
      start_date: taskData.start_date ? taskData.start_date.toISOString().split('T')[0] : null,
      end_date: taskData.end_date ? taskData.end_date.toISOString().split('T')[0] : null,
      created_at: taskData.created_at,
      updated_at: taskData.updated_at
    };

    // Include user info if available
    if (taskData.user_name) {
      task.user = {
        id: taskData.user_id,
        name: taskData.user_name,
        email: taskData.user_email
      };
    }

    // Include assigned_by user info if available
    if (taskData.assigned_by_name) {
      task.assigned_by_user = {
        id: taskData.assigned_by,
        name: taskData.assigned_by_name,
        email: taskData.assigned_by_email
      };
    }

    return task;
  }

  // Get all tasks with optional filters
  static async findAll(filters = {}) {
    let query = `
      SELECT t.*,
             u.name as user_name,
             u.email as user_email,
             ab.name as assigned_by_name,
             ab.email as assigned_by_email
      FROM tasks t
      LEFT JOIN users u ON t.user_id = u.id
      LEFT JOIN users ab ON t.assigned_by = ab.id
    `;
    
    const conditions = [];
    const values = [];
    
    if (filters.user_id) {
      conditions.push('t.user_id = ?');
      values.push(filters.user_id);
    }
    
    if (filters.status) {
      conditions.push('t.status = ?');
      values.push(filters.status);
    }
    
    if (filters.priority) {
      conditions.push('t.priority = ?');
      values.push(filters.priority);
    }
    
    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }
    
    query += ' ORDER BY t.created_at DESC';
    
    const results = await executeQuery(query, values);
    return results.map(task => new Task(task));
  }

  // Get tasks by user ID
  static async findByUserId(userId) {
    return await Task.findAll({ user_id: userId });
  }

  // Update task
  static async update(id, updateData) {
    const allowedFields = ['title', 'description', 'status', 'priority', 'user_id', 'assigned_by', 'start_date', 'end_date'];
    const updates = [];
    const values = [];

    for (const [key, value] of Object.entries(updateData)) {
      if (allowedFields.includes(key) && value !== undefined) {
        updates.push(`${key} = ?`);
        values.push(value);
      }
    }
    
    if (updates.length === 0) {
      throw new Error('No valid fields to update');
    }
    
    values.push(id);
    
    const query = `
      UPDATE tasks 
      SET ${updates.join(', ')}, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `;
    
    await executeQuery(query, values);
    return await Task.findById(id);
  }

  // Delete task
  static async delete(id) {
    const query = 'DELETE FROM tasks WHERE id = ?';
    const result = await executeQuery(query, [id]);
    return result.affectedRows > 0;
  }

  // Get task statistics
  static async getStats(userId = null) {
    let baseQuery = 'SELECT COUNT(*) as count FROM tasks';
    let userCondition = userId ? ' WHERE user_id = ?' : '';
    
    const queries = [
      `${baseQuery}${userCondition}`, // total
      `${baseQuery}${userCondition ? userCondition + ' AND' : ' WHERE'} status = 'todo'`,
      `${baseQuery}${userCondition ? userCondition + ' AND' : ' WHERE'} status = 'in_progress'`,
      `${baseQuery}${userCondition ? userCondition + ' AND' : ' WHERE'} status = 'done'`,
      `${baseQuery}${userCondition ? userCondition + ' AND' : ' WHERE'} priority = 'high'`,
      `${baseQuery}${userCondition ? userCondition + ' AND' : ' WHERE'} priority = 'medium'`,
      `${baseQuery}${userCondition ? userCondition + ' AND' : ' WHERE'} priority = 'low'`
    ];
    
    const params = userId ? [userId, userId, userId, userId, userId, userId, userId] : [];
    
    const results = await Promise.all(
      queries.map((query, index) => executeQuery(query, userId ? [params[index]] : []))
    );
    
    return {
      total_tasks: results[0][0].count,
      todo_tasks: results[1][0].count,
      in_progress_tasks: results[2][0].count,
      done_tasks: results[3][0].count,
      high_priority: results[4][0].count,
      medium_priority: results[5][0].count,
      low_priority: results[6][0].count
    };
  }

  // Get recent tasks
  static async getRecent(limit = 5, userId = null) {
    let query = `
      SELECT t.*, u.name as user_name, u.email as user_email
      FROM tasks t
      LEFT JOIN users u ON t.user_id = u.id
    `;
    
    if (userId) {
      query += ' WHERE t.user_id = ?';
    }
    
    query += ' ORDER BY t.created_at DESC LIMIT ?';
    
    const params = userId ? [userId, limit] : [limit];
    const results = await executeQuery(query, params);
    
    return results.map(task => new Task(task));
  }

  // Convert to JSON
  toJSON() {
    return {
      id: this.id,
      title: this.title,
      description: this.description,
      status: this.status,
      priority: this.priority,
      user_id: this.user_id,
      user: this.user,
      created_at: this.created_at,
      updated_at: this.updated_at
    };
  }
}

module.exports = Task;
