import React, { useState, useEffect } from 'react';
import { useNavigate, usePara<PERSON>, Link } from 'react-router-dom';
import { tasksAPI } from '../services/api';
import {
  ArrowLeft,
  Edit,
  Trash2,
  Calendar,
  User,
  Clock,
  Flag,
  CheckCircle,
  Circle,
  PlayCircle,
  AlertCircle,
  Eye,
  Zap,
  UserCheck,
  CalendarDays,
  CalendarX
} from 'lucide-react';
import Layout from '../components/Layout';
import LoadingSpinner from '../components/LoadingSpinner';
import toast from 'react-hot-toast';

const TaskDetail = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [task, setTask] = useState(null);
  const [loading, setLoading] = useState(true);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  // Format date helper function
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  useEffect(() => {
    fetchTask();
  }, [id]);

  const fetchTask = async () => {
    try {
      const response = await tasksAPI.getTask(id);
      if (response.success) {
        setTask(response.data.task);
      }
    } catch (error) {
      console.error('Failed to fetch task:', error);
      toast.error('Failed to load task');
      navigate('/tasks');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    try {
      const response = await tasksAPI.deleteTask(id);

      if (response.success) {
        toast.success('Task deleted successfully');
        navigate('/tasks');
      }
    } catch (error) {
      console.error('Failed to delete task:', error);
      toast.error('Failed to delete task');
    } finally {
      setShowDeleteModal(false);
    }
  };

  const getStatusConfig = (status) => {
    const configs = {
      todo: {
        icon: Circle,
        color: 'text-slate-400',
        bg: 'bg-slate-500/20',
        border: 'border-slate-500/30',
        label: 'To Do'
      },
      in_progress: {
        icon: PlayCircle,
        color: 'text-blue-400',
        bg: 'bg-blue-500/20',
        border: 'border-blue-500/30',
        label: 'In Progress'
      },
      done: {
        icon: CheckCircle,
        color: 'text-green-400',
        bg: 'bg-green-500/20',
        border: 'border-green-500/30',
        label: 'Done'
      }
    };
    return configs[status] || configs.todo;
  };

  const getPriorityConfig = (priority) => {
    const configs = {
      low: {
        color: 'text-green-400',
        bg: 'bg-green-500/20',
        border: 'border-green-500/30',
        label: 'Low Priority'
      },
      medium: {
        color: 'text-yellow-400',
        bg: 'bg-yellow-500/20',
        border: 'border-yellow-500/30',
        label: 'Medium Priority'
      },
      high: {
        color: 'text-red-400',
        bg: 'bg-red-500/20',
        border: 'border-red-500/30',
        label: 'High Priority'
      }
    };
    return configs[priority] || configs.medium;
  };

  const formatStatus = (status) => {
    return status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size="lg" />
        </div>
      </Layout>
    );
  }

  if (!task) {
    return (
      <Layout>
        <div className="min-h-screen bg-white flex items-center justify-center">
          <div className="text-center py-12 px-6 bg-slate-800/50 backdrop-blur-xl rounded-2xl border border-slate-700/50 shadow-2xl">
            <AlertCircle className="h-16 w-16 text-red-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-white mb-2">Task not found</h3>
            <p className="text-slate-400 mb-6">The task you're looking for doesn't exist or has been deleted.</p>
            <Link
              to="/tasks"
              className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-cyan-500 to-purple-600 text-white font-semibold rounded-xl hover:from-cyan-600 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Tasks
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  const statusConfig = getStatusConfig(task.status);
  const priorityConfig = getPriorityConfig(task.priority);
  const StatusIcon = statusConfig.icon;

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header - Matching Dashboard/TaskList style */}
        <div className="relative p-6 bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate('/tasks')}
                className="flex items-center justify-center w-12 h-12 bg-white/5 backdrop-blur-xl rounded-xl border border-white/10 text-gray-400 hover:text-cyan-400 hover:border-cyan-500/30 transition-all duration-300 group"
              >
                <ArrowLeft className="h-5 w-5 group-hover:scale-110 transition-transform" />
              </button>
              <div className="space-y-2">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-cyan-400 rounded-full animate-pulse"></div>
                  <h1 className="text-3xl font-bold bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
                    Task Details
                  </h1>
                  <Eye className="w-6 h-6 text-cyan-400 animate-pulse" />
                </div>
                <p className="text-gray-300 text-lg">
                  View and manage your <span className="text-cyan-400 font-semibold">task information</span>.
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <Link
                to={`/tasks/${task.id}/edit`}
                className="flex items-center px-6 py-3 bg-white/5 backdrop-blur-xl rounded-xl border border-white/10 text-gray-300 hover:text-cyan-400 hover:border-cyan-500/30 transition-all duration-300 group shadow-lg hover:shadow-cyan-500/20"
              >
                <Edit className="h-4 w-4 mr-2 group-hover:scale-110 transition-transform" />
                Edit
              </Link>
              <button
                onClick={() => setShowDeleteModal(true)}
                className="flex items-center px-6 py-3 bg-red-500/10 backdrop-blur-xl rounded-xl border border-red-500/30 text-red-400 hover:bg-red-500/20 hover:text-red-300 hover:border-red-400/50 transition-all duration-300 group shadow-lg hover:shadow-red-500/20"
              >
                <Trash2 className="h-4 w-4 mr-2 group-hover:scale-110 transition-transform" />
                Delete
              </button>
            </div>
          </div>
        </div>

        {/* Task Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Task Header Card - Matching Dashboard card style */}
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-br from-cyan-500/20 to-purple-500/20 rounded-xl blur-lg group-hover:blur-xl transition-all duration-300"></div>
              <div className="relative p-6 bg-white/5 backdrop-blur-xl border border-white/10 rounded-xl hover:border-cyan-500/30 transition-all duration-300">
                <div className="space-y-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h2 className="text-2xl font-bold text-white mb-4 group-hover:text-cyan-400 transition-colors">
                        {task.title}
                      </h2>
                      <div className="flex items-center space-x-4">
                        <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${statusConfig.bg} ${statusConfig.border} ${statusConfig.color} border`}>
                          <StatusIcon className="w-4 h-4 mr-2" />
                          {statusConfig.label}
                        </div>
                        <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${priorityConfig.bg} ${priorityConfig.border} ${priorityConfig.color} border`}>
                          <Flag className="w-4 h-4 mr-2" />
                          {priorityConfig.label}
                        </div>
                      </div>
                    </div>
                  </div>

                  {task.description && (
                    <div className="border-t border-white/10 pt-6">
                      <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                        <Edit className="w-5 h-5 mr-2 text-cyan-400" />
                        Description
                      </h3>
                      <div className="text-gray-300 whitespace-pre-wrap leading-relaxed bg-white/5 rounded-xl p-6 border border-white/10">
                        {task.description}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Task Info - Matching Dashboard card style */}
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-xl blur-lg group-hover:blur-xl transition-all duration-300"></div>
              <div className="relative p-6 bg-white/5 backdrop-blur-xl border border-white/10 rounded-xl hover:border-blue-500/30 transition-all duration-300 group-hover:scale-105">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-3">
                    <div className="p-3 bg-cyan-500/20 rounded-lg">
                      <Calendar className="h-6 w-6 text-cyan-400" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-white">Task Information</h3>
                      <p className="text-sm text-gray-400">Details and metadata</p>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center space-x-3 p-3 bg-white/5 rounded-lg border border-white/10">
                    <Calendar className="h-4 w-4 text-cyan-400" />
                    <div className="flex-1">
                      <p className="text-sm text-gray-400">Created</p>
                      <p className="text-white font-medium">{formatDate(task.created_at)}</p>
                    </div>
                  </div>

                  {task.updated_at !== task.created_at && (
                    <div className="flex items-center space-x-3 p-3 bg-white/5 rounded-lg border border-white/10">
                      <Clock className="h-4 w-4 text-purple-400" />
                      <div className="flex-1">
                        <p className="text-sm text-gray-400">Last Updated</p>
                        <p className="text-white font-medium">{formatDate(task.updated_at)}</p>
                      </div>
                    </div>
                  )}

                  {task.user_name && (
                    <div className="flex items-center space-x-3 p-3 bg-white/5 rounded-lg border border-white/10">
                      <User className="h-4 w-4 text-green-400" />
                      <div className="flex-1">
                        <p className="text-sm text-gray-400">Assigned to</p>
                        <p className="text-white font-medium">{task.user_name}</p>
                        <p className="text-gray-400 text-sm">{task.user_email}</p>
                      </div>
                    </div>
                  )}

                  {task.assigned_by_name && (
                    <div className="flex items-center space-x-3 p-3 bg-white/5 rounded-lg border border-white/10">
                      <UserCheck className="h-4 w-4 text-blue-400" />
                      <div className="flex-1">
                        <p className="text-sm text-gray-400">Assigned by</p>
                        <p className="text-white font-medium">{task.assigned_by_name}</p>
                        <p className="text-gray-400 text-sm">{task.assigned_by_email}</p>
                      </div>
                    </div>
                  )}

                  {task.start_date && (
                    <div className="flex items-center space-x-3 p-3 bg-white/5 rounded-lg border border-white/10">
                      <CalendarDays className="h-4 w-4 text-emerald-400" />
                      <div className="flex-1">
                        <p className="text-sm text-gray-400">Start Date</p>
                        <p className="text-white font-medium">{formatDate(task.start_date)}</p>
                      </div>
                    </div>
                  )}

                  {task.end_date && (
                    <div className="flex items-center space-x-3 p-3 bg-white/5 rounded-lg border border-white/10">
                      <CalendarX className="h-4 w-4 text-rose-400" />
                      <div className="flex-1">
                        <p className="text-sm text-gray-400">End Date</p>
                        <p className="text-white font-medium">{formatDate(task.end_date)}</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Quick Actions - Matching Dashboard card style */}
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-xl blur-lg group-hover:blur-xl transition-all duration-300"></div>
              <div className="relative p-6 bg-white/5 backdrop-blur-xl border border-white/10 rounded-xl hover:border-purple-500/30 transition-all duration-300 group-hover:scale-105">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-3">
                    <div className="p-3 bg-purple-500/20 rounded-lg">
                      <Zap className="h-6 w-6 text-purple-400" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-white">Quick Actions</h3>
                      <p className="text-sm text-gray-400">Manage this task</p>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <Link
                    to={`/tasks/${task.id}/edit`}
                    className="w-full flex items-center justify-center px-4 py-3 bg-gradient-to-r from-cyan-500 to-purple-600 hover:from-cyan-400 hover:to-purple-500 text-white shadow-lg hover:shadow-cyan-500/50 focus:ring-cyan-400 transform hover:scale-105 active:scale-95 relative overflow-hidden rounded-xl transition-all duration-300 group"
                  >
                    <Edit className="h-4 w-4 mr-2 group-hover:scale-110 transition-transform" />
                    Edit Task
                  </Link>

                  <button
                    onClick={() => setShowDeleteModal(true)}
                    className="w-full flex items-center justify-center px-4 py-3 bg-red-500/10 border border-red-500/30 text-red-400 rounded-xl hover:bg-red-500/20 hover:border-red-400/50 hover:text-red-300 transition-all duration-300 group"
                  >
                    <Trash2 className="h-4 w-4 mr-2 group-hover:scale-110 transition-transform" />
                    Delete Task
                  </button>

                  <Link
                    to="/tasks"
                    className="w-full flex items-center justify-center px-4 py-3 bg-white/5 hover:bg-cyan-500/20 text-cyan-400 hover:text-white border border-cyan-500/30 hover:border-cyan-400 focus:ring-cyan-400 transform hover:scale-105 active:scale-95 backdrop-blur-sm rounded-xl transition-all duration-300 group"
                  >
                    <ArrowLeft className="h-4 w-4 mr-2 group-hover:scale-110 transition-transform" />
                    Back to Tasks
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Delete Confirmation Modal - Matching app design */}
        {showDeleteModal && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <div className="relative max-w-md w-full">
              <div className="absolute inset-0 bg-gradient-to-br from-red-500/20 to-pink-500/20 rounded-2xl blur-lg"></div>
              <div className="relative bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl shadow-2xl">
                <div className="p-6">
                  <div className="flex items-center mb-6">
                    <div className="flex-shrink-0 w-12 h-12 bg-red-500/20 rounded-xl flex items-center justify-center border border-red-500/30 mr-4">
                      <AlertCircle className="h-6 w-6 text-red-400" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-white">Delete Task</h3>
                      <p className="text-gray-400 text-sm">This action cannot be undone</p>
                    </div>
                  </div>

                  <p className="text-gray-300 mb-6 leading-relaxed">
                    Are you sure you want to delete "<span className="font-semibold text-cyan-400">{task.title}</span>"?
                    This will permanently remove the task and all associated data.
                  </p>

                  <div className="flex space-x-3">
                    <button
                      onClick={() => setShowDeleteModal(false)}
                      className="flex-1 px-4 py-3 bg-white/5 hover:bg-cyan-500/20 text-cyan-400 hover:text-white border border-cyan-500/30 hover:border-cyan-400 focus:ring-cyan-400 transform hover:scale-105 active:scale-95 backdrop-blur-sm rounded-xl transition-all duration-300"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleDelete}
                      className="flex-1 px-4 py-3 bg-red-500/10 border border-red-500/30 text-red-400 rounded-xl hover:bg-red-500/20 hover:border-red-400/50 hover:text-red-300 transition-all duration-300"
                    >
                      Delete Task
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default TaskDetail;
