const express = require('express');
const router = express.Router();
const Notification = require('../models/Notification');
const { authenticateToken } = require('../middleware/auth');

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Get user's notifications
router.get('/', async (req, res) => {
  try {
    const userId = req.user.id;
    const { limit = 20, offset = 0, unread_only = false } = req.query;
    
    const options = {
      limit: parseInt(limit),
      offset: parseInt(offset),
      unreadOnly: unread_only === 'true'
    };
    
    const notifications = await Notification.findByUserId(userId, options);
    const unreadCount = await Notification.getUnreadCount(userId);
    
    res.json({
      success: true,
      message: 'Notifications retrieved successfully',
      data: {
        notifications,
        unread_count: unreadCount,
        pagination: {
          limit: options.limit,
          offset: options.offset,
          has_more: notifications.length === options.limit
        }
      }
    });
  } catch (error) {
    console.error('Get notifications error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve notifications',
      error: error.message
    });
  }
});

// Get unread notifications count
router.get('/unread-count', async (req, res) => {
  try {
    const userId = req.user.id;
    const count = await Notification.getUnreadCount(userId);
    
    res.json({
      success: true,
      message: 'Unread count retrieved successfully',
      data: {
        unread_count: count
      }
    });
  } catch (error) {
    console.error('Get unread count error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve unread count',
      error: error.message
    });
  }
});

// Mark notification as read
router.patch('/:id/read', async (req, res) => {
  try {
    const userId = req.user.id;
    const notificationId = req.params.id;
    
    const success = await Notification.markAsRead(notificationId, userId);
    
    if (!success) {
      return res.status(404).json({
        success: false,
        message: 'Notification not found or already read'
      });
    }
    
    res.json({
      success: true,
      message: 'Notification marked as read'
    });
  } catch (error) {
    console.error('Mark notification as read error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to mark notification as read',
      error: error.message
    });
  }
});

// Mark all notifications as read
router.patch('/mark-all-read', async (req, res) => {
  try {
    const userId = req.user.id;
    const updatedCount = await Notification.markAllAsRead(userId);
    
    res.json({
      success: true,
      message: `${updatedCount} notifications marked as read`,
      data: {
        updated_count: updatedCount
      }
    });
  } catch (error) {
    console.error('Mark all notifications as read error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to mark all notifications as read',
      error: error.message
    });
  }
});

// Delete notification
router.delete('/:id', async (req, res) => {
  try {
    const userId = req.user.id;
    const notificationId = req.params.id;
    
    const success = await Notification.delete(notificationId, userId);
    
    if (!success) {
      return res.status(404).json({
        success: false,
        message: 'Notification not found'
      });
    }
    
    res.json({
      success: true,
      message: 'Notification deleted successfully'
    });
  } catch (error) {
    console.error('Delete notification error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete notification',
      error: error.message
    });
  }
});

// Get specific notification
router.get('/:id', async (req, res) => {
  try {
    const userId = req.user.id;
    const notificationId = req.params.id;
    
    const notification = await Notification.findById(notificationId);
    
    if (!notification || notification.user_id !== userId) {
      return res.status(404).json({
        success: false,
        message: 'Notification not found'
      });
    }
    
    res.json({
      success: true,
      message: 'Notification retrieved successfully',
      data: {
        notification
      }
    });
  } catch (error) {
    console.error('Get notification error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve notification',
      error: error.message
    });
  }
});

// Admin route: Create notification for specific user
router.post('/admin/create', async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Admin privileges required.'
      });
    }
    
    const { user_id, message, type = 'info', task_id = null } = req.body;
    
    if (!user_id || !message) {
      return res.status(400).json({
        success: false,
        message: 'User ID and message are required'
      });
    }
    
    const notification = await Notification.create({
      user_id,
      message,
      type,
      task_id,
      related_user_id: req.user.id
    });
    
    res.status(201).json({
      success: true,
      message: 'Notification created successfully',
      data: {
        notification
      }
    });
  } catch (error) {
    console.error('Create notification error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create notification',
      error: error.message
    });
  }
});

// Admin route: Get all notifications
router.get('/admin/all', async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Admin privileges required.'
      });
    }
    
    const { limit = 50, offset = 0 } = req.query;
    
    const query = `
      SELECT n.*, u.name as user_name, u.email as user_email,
             ru.name as related_user_name, ru.email as related_user_email,
             t.title as task_title
      FROM notifications n
      LEFT JOIN users u ON n.user_id = u.id
      LEFT JOIN users ru ON n.related_user_id = ru.id
      LEFT JOIN tasks t ON n.task_id = t.id
      ORDER BY n.created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    const { executeQuery } = require('../models/database');
    const notifications = await executeQuery(query, [parseInt(limit), parseInt(offset)]);
    
    res.json({
      success: true,
      message: 'All notifications retrieved successfully',
      data: {
        notifications,
        pagination: {
          limit: parseInt(limit),
          offset: parseInt(offset),
          has_more: notifications.length === parseInt(limit)
        }
      }
    });
  } catch (error) {
    console.error('Get all notifications error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve notifications',
      error: error.message
    });
  }
});

module.exports = router;
