import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { tasksAPI } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import { ArrowLeft, Plus, Edit3, FileText, Flag, CheckCircle, Clock, AlertCircle, User, Calendar } from 'lucide-react';
import Layout from '../components/Layout';
import LoadingSpinner from '../components/LoadingSpinner';
import toast from 'react-hot-toast';

const TaskForm = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const { user } = useAuth();
  const isEditing = Boolean(id);

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    status: 'todo',
    priority: 'medium',
    user_id: '',
    start_date: '',
    end_date: ''
  });
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(isEditing);
  const [errors, setErrors] = useState({});
  const [users, setUsers] = useState([]);
  const [loadingUsers, setLoadingUsers] = useState(false);

  useEffect(() => {
    if (isEditing) {
      fetchTask();
    }
    // Fetch users for assignment if user is admin
    if (user?.role === 'admin') {
      fetchUsers();
    }
  }, [id, isEditing, user]);

  const fetchTask = async () => {
    try {
      const response = await tasksAPI.getTask(id);
      if (response.success) {
        const task = response.data.task;
        setFormData({
          title: task.title,
          description: task.description || '',
          status: task.status,
          priority: task.priority,
          user_id: task.user_id || '',
          start_date: task.start_date || '',
          end_date: task.end_date || ''
        });
      }
    } catch (error) {
      console.error('Failed to fetch task:', error);
      toast.error('Failed to load task');
      navigate('/tasks');
    } finally {
      setInitialLoading(false);
    }
  };

  const fetchUsers = async () => {
    try {
      setLoadingUsers(true);
      const response = await tasksAPI.getUsersForAssignment();
      if (response.success) {
        setUsers(response.data.users);
      }
    } catch (error) {
      console.error('Failed to fetch users:', error);
      toast.error('Failed to load users');
    } finally {
      setLoadingUsers(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);
      
      const taskData = {
        title: formData.title.trim(),
        description: formData.description.trim(),
        status: formData.status,
        priority: formData.priority,
        user_id: formData.user_id || undefined,
        start_date: formData.start_date || undefined,
        end_date: formData.end_date || undefined
      };

      let response;
      if (isEditing) {
        response = await tasksAPI.updateTask(id, taskData);
      } else {
        response = await tasksAPI.createTask(taskData);
      }

      if (response.success) {
        toast.success(`Task ${isEditing ? 'updated' : 'created'} successfully`);
        navigate('/tasks');
      }
    } catch (error) {
      console.error('Failed to save task:', error);
      toast.error(`Failed to ${isEditing ? 'update' : 'create'} task`);
    } finally {
      setLoading(false);
    }
  };

  if (initialLoading) {
    return (
      <Layout>
        <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-16 w-16 border-2 border-cyan-400 border-t-transparent mx-auto"></div>
            <p className="mt-4 text-cyan-300 font-medium">Loading task...</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6">
        <div className="max-w-4xl mx-auto space-y-8">
          {/* Header */}
          <div className="relative p-6 bg-slate-800/50 backdrop-blur-xl rounded-2xl border border-slate-700/50 overflow-hidden">
            {/* Background Effects */}
            <div className="absolute inset-0">
              <div className="absolute top-0 left-0 w-32 h-32 bg-cyan-500/20 rounded-full blur-3xl"></div>
              <div className="absolute bottom-0 right-0 w-40 h-40 bg-purple-500/20 rounded-full blur-3xl"></div>
            </div>

            <div className="relative flex items-center space-x-6">
              <button
                onClick={() => navigate('/tasks')}
                className="group p-3 bg-slate-700/50 hover:bg-slate-600/50 rounded-xl border border-slate-600/50 hover:border-cyan-500/50 transition-all duration-200"
              >
                <ArrowLeft className="h-6 w-6 text-slate-300 group-hover:text-cyan-400 transition-colors" />
              </button>
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-2">
                  <div className="w-10 h-10 bg-gradient-to-r from-cyan-500 to-purple-500 rounded-lg flex items-center justify-center">
                    {isEditing ? <Edit3 className="w-5 h-5 text-white" /> : <Plus className="w-5 h-5 text-white" />}
                  </div>
                  <h1 className="text-3xl font-bold bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">
                    {isEditing ? 'Edit Task' : 'Create New Task'}
                  </h1>
                </div>
                <p className="text-slate-300 text-lg">
                  {isEditing ? 'Update task details and settings' : 'Add a new task to your workflow'}
                </p>
              </div>
            </div>
          </div>

          {/* Form */}
          <div className="relative p-8 bg-slate-800/50 backdrop-blur-xl rounded-2xl border border-slate-700/50 shadow-2xl">
            {/* Background Effects */}
            <div className="absolute inset-0">
              <div className="absolute top-0 right-0 w-32 h-32 bg-blue-500/10 rounded-full blur-2xl"></div>
              <div className="absolute bottom-0 left-0 w-24 h-24 bg-cyan-500/10 rounded-full blur-2xl"></div>
            </div>

            <form onSubmit={handleSubmit} className="relative space-y-8">
              {/* Title Field */}
              <div className="space-y-3">
                <label htmlFor="title" className="block text-sm font-semibold text-white mb-3 flex items-center gap-2">
                  <FileText className="w-4 h-4 text-cyan-400" />
                  Task Title *
                </label>
                <div className="relative group">
                  <input
                    type="text"
                    id="title"
                    name="title"
                    value={formData.title}
                    onChange={handleChange}
                    className={`w-full px-4 py-4 bg-slate-700/50 border rounded-xl text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:border-transparent transition-all duration-200 ${
                      errors.title ? 'border-red-500 ring-2 ring-red-500/20' : 'border-slate-600'
                    }`}
                    placeholder="Enter a descriptive task title..."
                  />
                </div>
                {errors.title && (
                  <p className="text-sm text-red-400 flex items-center gap-1">
                    <AlertCircle className="w-4 h-4" />
                    {errors.title}
                  </p>
                )}
              </div>

              {/* Description Field */}
              <div className="space-y-3">
                <label htmlFor="description" className="block text-sm font-semibold text-white mb-3 flex items-center gap-2">
                  <FileText className="w-4 h-4 text-purple-400" />
                  Description
                </label>
                <div className="relative group">
                  <textarea
                    id="description"
                    name="description"
                    rows={4}
                    value={formData.description}
                    onChange={handleChange}
                    className="w-full px-4 py-4 bg-slate-700/50 border border-slate-600 rounded-xl text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-200 resize-none"
                    placeholder="Provide additional details about this task..."
                  />
                </div>
              </div>

              {/* Status and Priority Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {/* Status Field */}
                <div className="space-y-3">
                  <label htmlFor="status" className="block text-sm font-semibold text-white mb-3 flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-green-400" />
                    Status
                  </label>
                  <div className="relative group">
                    <select
                      id="status"
                      name="status"
                      value={formData.status}
                      onChange={handleChange}
                      className="w-full px-4 py-4 bg-slate-700/50 border border-slate-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-green-400 focus:border-transparent transition-all duration-200 appearance-none cursor-pointer"
                    >
                      <option value="todo" className="bg-slate-800 text-white">📋 Todo</option>
                      <option value="in_progress" className="bg-slate-800 text-white">⚡ In Progress</option>
                      <option value="done" className="bg-slate-800 text-white">✅ Done</option>
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
                      <svg className="w-5 h-5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </div>
                </div>

                {/* Priority Field */}
                <div className="space-y-3">
                  <label htmlFor="priority" className="block text-sm font-semibold text-white mb-3 flex items-center gap-2">
                    <Flag className="w-4 h-4 text-orange-400" />
                    Priority
                  </label>
                  <div className="relative group">
                    <select
                      id="priority"
                      name="priority"
                      value={formData.priority}
                      onChange={handleChange}
                      className="w-full px-4 py-4 bg-slate-700/50 border border-slate-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-orange-400 focus:border-transparent transition-all duration-200 appearance-none cursor-pointer"
                    >
                      <option value="low" className="bg-slate-800 text-white">🟢 Low Priority</option>
                      <option value="medium" className="bg-slate-800 text-white">🟡 Medium Priority</option>
                      <option value="high" className="bg-slate-800 text-white">🔴 High Priority</option>
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
                      <svg className="w-5 h-5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>

              {/* User Assignment and Dates Grid */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {/* User Assignment Field - Only for Admins */}
                {user?.role === 'admin' && (
                  <div className="space-y-3">
                    <label htmlFor="user_id" className="block text-sm font-semibold text-white mb-3 flex items-center gap-2">
                      <User className="w-4 h-4 text-blue-400" />
                      Assign To
                    </label>
                    <div className="relative group">
                      <select
                        id="user_id"
                        name="user_id"
                        value={formData.user_id}
                        onChange={handleChange}
                        className="w-full px-4 py-4 bg-slate-700/50 border border-slate-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent transition-all duration-200 appearance-none cursor-pointer"
                      >
                        <option value="" className="bg-slate-800 text-white">Select User</option>
                        {users.map(user => (
                          <option key={user.id} value={user.id} className="bg-slate-800 text-white">
                            {user.name} ({user.role})
                          </option>
                        ))}
                      </select>
                      <div className="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
                        <svg className="w-5 h-5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </div>
                      {loadingUsers && (
                        <div className="absolute inset-y-0 right-8 flex items-center">
                          <LoadingSpinner size="sm" />
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Start Date Field */}
                <div className="space-y-3">
                  <label htmlFor="start_date" className="block text-sm font-semibold text-white mb-3 flex items-center gap-2">
                    <Calendar className="w-4 h-4 text-green-400" />
                    Start Date
                  </label>
                  <div className="relative group">
                    <input
                      id="start_date"
                      name="start_date"
                      type="date"
                      value={formData.start_date}
                      onChange={handleChange}
                      className="w-full px-4 py-4 bg-slate-700/50 border border-slate-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-green-400 focus:border-transparent transition-all duration-200"
                    />
                  </div>
                </div>

                {/* End Date Field */}
                <div className="space-y-3">
                  <label htmlFor="end_date" className="block text-sm font-semibold text-white mb-3 flex items-center gap-2">
                    <Calendar className="w-4 h-4 text-red-400" />
                    End Date
                  </label>
                  <div className="relative group">
                    <input
                      id="end_date"
                      name="end_date"
                      type="date"
                      value={formData.end_date}
                      onChange={handleChange}
                      min={formData.start_date}
                      className="w-full px-4 py-4 bg-slate-700/50 border border-slate-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-red-400 focus:border-transparent transition-all duration-200"
                    />
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end space-x-4 pt-8 border-t border-slate-700/50">
                <button
                  type="button"
                  onClick={() => navigate('/tasks')}
                  className="px-6 py-3 bg-slate-700/50 hover:bg-slate-600/50 text-slate-300 hover:text-white font-medium rounded-xl border border-slate-600/50 hover:border-slate-500/50 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-slate-500/50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className={`px-8 py-3 rounded-xl font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:ring-offset-2 focus:ring-offset-slate-800 ${
                    loading
                      ? 'bg-slate-600 cursor-not-allowed text-slate-400'
                      : 'bg-gradient-to-r from-cyan-500 to-purple-500 hover:from-cyan-400 hover:to-purple-400 text-white shadow-lg hover:shadow-cyan-500/25 transform hover:scale-105'
                  }`}
                >
                  {loading ? (
                    <div className="flex items-center gap-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-2 border-slate-400 border-t-transparent"></div>
                      {isEditing ? 'Updating...' : 'Creating...'}
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      {isEditing ? <Edit3 className="w-4 h-4" /> : <Plus className="w-4 h-4" />}
                      {isEditing ? 'Update Task' : 'Create Task'}
                    </div>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default TaskForm;
