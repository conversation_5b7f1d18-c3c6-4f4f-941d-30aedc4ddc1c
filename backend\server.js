const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const authRoutes = require('./routes/auth');
const taskRoutes = require('./routes/tasks');
const adminRoutes = require('./routes/admin');
const notificationRoutes = require('./routes/notifications');
const { connectDB } = require('./models/database');

const app = express();
const PORT = process.env.PORT || 5000;

// Security middleware
app.use(helmet());

// Rate limiting (disabled for development)
if (process.env.NODE_ENV === 'production') {
  const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: 'Too many requests from this IP, please try again later.'
  });
  app.use(limiter);
}

// CORS configuration
app.use(cors({
  origin: [
    process.env.FRONTEND_URL || 'http://localhost:5173',
    'http://localhost:5174',
    'http://localhost:3000'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    message: 'Task Management API is running',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// Add missing columns to tasks table
app.post('/api/add-task-columns', async (req, res) => {
  try {
    const { executeQuery } = require('./models/database');
    console.log('🔧 Adding missing columns to tasks table...');

    // Add assigned_by column
    try {
      await executeQuery(`
        ALTER TABLE tasks
        ADD COLUMN assigned_by INT NULL,
        ADD FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE SET NULL
      `);
      console.log('✅ Added assigned_by column');
    } catch (error) {
      if (!error.message.includes('Duplicate column name')) {
        console.log('⚠️ assigned_by column might already exist:', error.message);
      }
    }

    // Add start_date column
    try {
      await executeQuery(`
        ALTER TABLE tasks
        ADD COLUMN start_date DATE NULL
      `);
      console.log('✅ Added start_date column');
    } catch (error) {
      if (!error.message.includes('Duplicate column name')) {
        console.log('⚠️ start_date column might already exist:', error.message);
      }
    }

    // Add end_date column
    try {
      await executeQuery(`
        ALTER TABLE tasks
        ADD COLUMN end_date DATE NULL
      `);
      console.log('✅ Added end_date column');
    } catch (error) {
      if (!error.message.includes('Duplicate column name')) {
        console.log('⚠️ end_date column might already exist:', error.message);
      }
    }

    res.json({
      success: true,
      message: 'Task table columns added successfully'
    });
  } catch (error) {
    console.error('❌ Failed to add task columns:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add task columns',
      error: error.message
    });
  }
});

// Database setup endpoint (for development)
app.post('/api/setup-database', async (req, res) => {
  try {
    const { executeQuery } = require('./models/database');
    const bcrypt = require('bcryptjs');

    // Disable foreign key checks temporarily
    await executeQuery('SET FOREIGN_KEY_CHECKS = 0');

    // Drop existing tables if they exist (for clean setup)
    await executeQuery('DROP TABLE IF EXISTS notifications');
    await executeQuery('DROP TABLE IF EXISTS tasks');
    await executeQuery('DROP TABLE IF EXISTS users');

    // Re-enable foreign key checks
    await executeQuery('SET FOREIGN_KEY_CHECKS = 1');

    // Create users table
    await executeQuery(`
      CREATE TABLE users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        role ENUM('admin', 'user') DEFAULT 'user',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    // Create tasks table
    await executeQuery(`
      CREATE TABLE tasks (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(200) NOT NULL,
        description TEXT,
        status ENUM('todo', 'in_progress', 'done') DEFAULT 'todo',
        priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
        user_id INT NOT NULL,
        assigned_by INT NULL,
        start_date DATE NULL,
        end_date DATE NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE SET NULL
      )
    `);

    // Create notifications table
    await executeQuery(`
      CREATE TABLE notifications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        message TEXT NOT NULL,
        type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
        task_id INT NULL,
        related_user_id INT NULL,
        is_read BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
        FOREIGN KEY (related_user_id) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_user_read (user_id, is_read),
        INDEX idx_created_at (created_at)
      )
    `);

    // Check if admin user exists
    const existingAdmin = await executeQuery('SELECT id FROM users WHERE email = ?', ['<EMAIL>']);

    if (existingAdmin.length === 0) {
      // Create admin user
      const hashedPassword = await bcrypt.hash('admin123', 10);
      await executeQuery(
        'INSERT INTO users (name, email, password, role) VALUES (?, ?, ?, ?)',
        ['Admin User', '<EMAIL>', hashedPassword, 'admin']
      );

      // Create sample user
      const userPassword = await bcrypt.hash('user123', 10);
      await executeQuery(
        'INSERT INTO users (name, email, password, role) VALUES (?, ?, ?, ?)',
        ['John Doe', '<EMAIL>', userPassword, 'user']
      );
    }

    // Add sample tasks
    const adminUser = await executeQuery('SELECT id FROM users WHERE email = ?', ['<EMAIL>']);
    const regularUser = await executeQuery('SELECT id FROM users WHERE email = ?', ['<EMAIL>']);

    if (adminUser.length > 0) {
      const adminId = adminUser[0].id;
      const userId = regularUser.length > 0 ? regularUser[0].id : adminId;

      // Insert sample tasks
      const sampleTasks = [
        ['Setup Development Environment', 'Configure local development environment with all necessary tools', 'done', 'high', adminId],
        ['Design Database Schema', 'Create comprehensive database schema for the task management system', 'done', 'high', adminId],
        ['Implement User Authentication', 'Build JWT-based authentication system with role-based access', 'in_progress', 'high', adminId],
        ['Create Task CRUD Operations', 'Implement create, read, update, delete operations for tasks', 'todo', 'medium', adminId],
        ['Build Admin Dashboard', 'Create admin panel for user and task management', 'todo', 'medium', adminId],
        ['Write Unit Tests', 'Implement comprehensive test suite for all components', 'todo', 'low', userId],
        ['Deploy to Production', 'Set up production deployment pipeline', 'todo', 'low', userId]
      ];

      for (const task of sampleTasks) {
        await executeQuery(
          'INSERT INTO tasks (title, description, status, priority, user_id) VALUES (?, ?, ?, ?, ?)',
          task
        );
      }
    }

    res.json({
      success: true,
      message: 'Database setup completed successfully',
      admin_credentials: {
        email: '<EMAIL>',
        password: 'admin123'
      },
      sample_data: 'Added sample tasks and users'
    });
  } catch (error) {
    console.error('Database setup error:', error);
    res.status(500).json({
      success: false,
      message: 'Database setup failed',
      error: error.message
    });
  }
});

// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/tasks', taskRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/notifications', notificationRoutes);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'API endpoint not found',
    path: req.originalUrl
  });
});

// Global error handler
app.use((err, req, res, next) => {
  console.error('Error:', err.stack);
  
  res.status(err.status || 500).json({
    success: false,
    message: err.message || 'Internal server error',
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
  });
});

// Initialize database connection and start server
const startServer = async () => {
  try {
    await connectDB();
    console.log('✅ Database connected successfully');
    
    app.listen(PORT, () => {
      console.log(`🚀 Server running on http://localhost:${PORT}`);
      console.log(`📊 API Health: http://localhost:${PORT}/api/health`);
      console.log(`🔐 Auth endpoints: http://localhost:${PORT}/api/auth`);
      console.log(`📋 Task endpoints: http://localhost:${PORT}/api/tasks`);
      console.log(`👑 Admin endpoints: http://localhost:${PORT}/api/admin`);
      console.log(`🔔 Notification endpoints: http://localhost:${PORT}/api/notifications`);
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error.message);
    process.exit(1);
  }
};

// Handle graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  process.exit(0);
});

startServer();

module.exports = app;
